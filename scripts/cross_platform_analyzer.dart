#!/usr/bin/env dart

import 'dart:io';
import 'dart:convert';

/// Cross-platform code analyzer for DassoShu Reader
/// 
/// Analyzes the codebase for potential cross-platform compatibility issues
/// and provides recommendations for improvements.
void main(List<String> args) async {
  print('🔍 DassoShu Reader Cross-Platform Code Analyzer');
  print('================================================');
  
  final analyzer = CrossPlatformAnalyzer();
  
  if (args.contains('--help') || args.contains('-h')) {
    analyzer.printHelp();
    return;
  }
  
  final verbose = args.contains('--verbose') || args.contains('-v');
  final fixMode = args.contains('--fix');
  final jsonOutput = args.contains('--json');
  
  try {
    final results = await analyzer.analyzeProject(
      verbose: verbose,
      fixMode: fixMode,
    );
    
    if (jsonOutput) {
      print(jsonEncode(results.toJson()));
    } else {
      analyzer.printResults(results);
    }
    
    // Exit with error code if critical issues found
    if (results.hasErrors) {
      exit(1);
    }
  } catch (e) {
    print('❌ Analysis failed: $e');
    exit(1);
  }
}

class CrossPlatformAnalyzer {
  static const String _libPath = 'lib';
  
  /// Analyzes the entire project for cross-platform issues
  Future<AnalysisResults> analyzeProject({
    bool verbose = false,
    bool fixMode = false,
  }) async {
    print('📁 Scanning project files...');
    
    final dartFiles = await _findDartFiles();
    final issues = <AnalysisIssue>[];
    
    print('📄 Found ${dartFiles.length} Dart files to analyze');
    
    for (final file in dartFiles) {
      if (verbose) {
        print('  Analyzing: ${file.path}');
      }
      
      final fileIssues = await _analyzeFile(file);
      issues.addAll(fileIssues);
      
      if (fixMode && fileIssues.isNotEmpty) {
        await _attemptAutoFix(file, fileIssues);
      }
    }
    
    return AnalysisResults(
      totalFiles: dartFiles.length,
      issues: issues,
      timestamp: DateTime.now(),
    );
  }
  
  /// Finds all Dart files in the project
  Future<List<File>> _findDartFiles() async {
    final libDir = Directory(_libPath);
    if (!libDir.existsSync()) {
      throw Exception('lib directory not found');
    }
    
    final dartFiles = <File>[];
    
    await for (final entity in libDir.list(recursive: true)) {
      if (entity is File && entity.path.endsWith('.dart')) {
        // Skip generated files
        if (!entity.path.contains('.g.dart') && 
            !entity.path.contains('.freezed.dart') &&
            !entity.path.contains('generated/')) {
          dartFiles.add(entity);
        }
      }
    }
    
    return dartFiles;
  }
  
  /// Analyzes a single file for cross-platform issues
  Future<List<AnalysisIssue>> _analyzeFile(File file) async {
    final content = await file.readAsString();
    final lines = content.split('\n');
    final issues = <AnalysisIssue>[];
    
    for (int i = 0; i < lines.length; i++) {
      final line = lines[i];
      final lineNumber = i + 1;
      
      // Check for direct platform usage
      if (line.contains('Platform.isIOS') || line.contains('Platform.isAndroid')) {
        issues.add(AnalysisIssue(
          type: IssueType.platformCheck,
          severity: IssueSeverity.error,
          file: file.path,
          line: lineNumber,
          message: 'Use PlatformAdaptations instead of direct Platform checks',
          suggestion: 'Replace with PlatformAdaptations.isIOS or PlatformAdaptations.isAndroid',
        ));
      }
      
      // Check for hardcoded navigation
      if (line.contains('MaterialPageRoute') || line.contains('CupertinoPageRoute')) {
        issues.add(AnalysisIssue(
          type: IssueType.navigation,
          severity: IssueSeverity.error,
          file: file.path,
          line: lineNumber,
          message: 'Use adaptive navigation instead of platform-specific routes',
          suggestion: 'Replace with AdaptiveNavigation.push() or PlatformAdaptations.createPageRoute()',
        ));
      }
      
      // Check for hardcoded dialogs
      if (line.contains('AlertDialog') || line.contains('CupertinoAlertDialog')) {
        issues.add(AnalysisIssue(
          type: IssueType.dialog,
          severity: IssueSeverity.warning,
          file: file.path,
          line: lineNumber,
          message: 'Consider using adaptive dialogs for better cross-platform UX',
          suggestion: 'Use AdaptiveDialogs.showAlert() for consistent platform behavior',
        ));
      }
      
      // Check for hardcoded icons
      if (line.contains('Icons.') && line.contains('CupertinoIcons.')) {
        issues.add(AnalysisIssue(
          type: IssueType.icons,
          severity: IssueSeverity.info,
          file: file.path,
          line: lineNumber,
          message: 'Consider using adaptive icons for consistency',
          suggestion: 'Use AdaptiveIcons for platform-appropriate icon selection',
        ));
      }
      
      // Check for localhost usage (iOS compatibility)
      if (line.contains('localhost') && !line.contains('127.0.0.1')) {
        issues.add(AnalysisIssue(
          type: IssueType.network,
          severity: IssueSeverity.warning,
          file: file.path,
          line: lineNumber,
          message: 'Use 127.0.0.1 instead of localhost for better iOS compatibility',
          suggestion: 'Replace localhost with 127.0.0.1 for WebView compatibility',
        ));
      }
      
      // Check for missing platform validation
      if (line.contains('WebView') || line.contains('InAppWebView')) {
        if (!content.contains('CrossPlatformValidator') && 
            !content.contains('PlatformFeatureDetector')) {
          issues.add(AnalysisIssue(
            type: IssueType.validation,
            severity: IssueSeverity.info,
            file: file.path,
            line: lineNumber,
            message: 'Consider adding platform validation for WebView usage',
            suggestion: 'Use CrossPlatformValidator to ensure WebView compatibility',
          ));
        }
      }
    }
    
    return issues;
  }
  
  /// Attempts to automatically fix issues where possible
  Future<void> _attemptAutoFix(File file, List<AnalysisIssue> issues) async {
    var content = await file.readAsString();
    bool modified = false;
    
    for (final issue in issues) {
      switch (issue.type) {
        case IssueType.platformCheck:
          // Auto-fix platform checks
          content = content.replaceAll('Platform.isIOS', 'PlatformAdaptations.isIOS');
          content = content.replaceAll('Platform.isAndroid', 'PlatformAdaptations.isAndroid');
          modified = true;
          break;
        case IssueType.network:
          // Auto-fix localhost usage
          content = content.replaceAll('localhost', '127.0.0.1');
          modified = true;
          break;
        default:
          // Other issues require manual intervention
          break;
      }
    }
    
    if (modified) {
      await file.writeAsString(content);
      print('🔧 Auto-fixed issues in: ${file.path}');
    }
  }
  
  /// Prints analysis results
  void printResults(AnalysisResults results) {
    print('\n📊 Analysis Results');
    print('==================');
    print('Files analyzed: ${results.totalFiles}');
    print('Issues found: ${results.issues.length}');
    
    final errorCount = results.issues.where((i) => i.severity == IssueSeverity.error).length;
    final warningCount = results.issues.where((i) => i.severity == IssueSeverity.warning).length;
    final infoCount = results.issues.where((i) => i.severity == IssueSeverity.info).length;
    
    print('  Errors: $errorCount');
    print('  Warnings: $warningCount');
    print('  Info: $infoCount');
    
    if (results.issues.isNotEmpty) {
      print('\n🔍 Issues by Category:');
      
      final groupedIssues = <IssueType, List<AnalysisIssue>>{};
      for (final issue in results.issues) {
        groupedIssues.putIfAbsent(issue.type, () => []).add(issue);
      }
      
      for (final entry in groupedIssues.entries) {
        print('\n${_getIssueTypeIcon(entry.key)} ${_getIssueTypeName(entry.key)} (${entry.value.length})');
        
        for (final issue in entry.value.take(5)) { // Show first 5 issues per category
          final severityIcon = _getSeverityIcon(issue.severity);
          print('  $severityIcon ${issue.file}:${issue.line} - ${issue.message}');
          if (issue.suggestion.isNotEmpty) {
            print('    💡 ${issue.suggestion}');
          }
        }
        
        if (entry.value.length > 5) {
          print('    ... and ${entry.value.length - 5} more');
        }
      }
    }
    
    if (results.hasErrors) {
      print('\n❌ Analysis completed with errors. Please fix critical issues before proceeding.');
    } else if (results.hasWarnings) {
      print('\n⚠️  Analysis completed with warnings. Consider addressing these for better cross-platform compatibility.');
    } else {
      print('\n✅ Analysis completed successfully! No critical cross-platform issues found.');
    }
  }
  
  String _getIssueTypeIcon(IssueType type) {
    switch (type) {
      case IssueType.platformCheck: return '📱';
      case IssueType.navigation: return '🧭';
      case IssueType.dialog: return '💬';
      case IssueType.icons: return '🎨';
      case IssueType.network: return '🌐';
      case IssueType.validation: return '✅';
    }
  }
  
  String _getIssueTypeName(IssueType type) {
    switch (type) {
      case IssueType.platformCheck: return 'Platform Checks';
      case IssueType.navigation: return 'Navigation';
      case IssueType.dialog: return 'Dialogs';
      case IssueType.icons: return 'Icons';
      case IssueType.network: return 'Network';
      case IssueType.validation: return 'Validation';
    }
  }
  
  String _getSeverityIcon(IssueSeverity severity) {
    switch (severity) {
      case IssueSeverity.error: return '❌';
      case IssueSeverity.warning: return '⚠️';
      case IssueSeverity.info: return 'ℹ️';
    }
  }
  
  void printHelp() {
    print('''
DassoShu Reader Cross-Platform Code Analyzer

Usage: dart scripts/cross_platform_analyzer.dart [options]

Options:
  -h, --help      Show this help message
  -v, --verbose   Show detailed analysis progress
  --fix           Attempt to automatically fix issues where possible
  --json          Output results in JSON format

Examples:
  dart scripts/cross_platform_analyzer.dart
  dart scripts/cross_platform_analyzer.dart --verbose
  dart scripts/cross_platform_analyzer.dart --fix
  dart scripts/cross_platform_analyzer.dart --json > analysis_results.json
''');
  }
}

class AnalysisResults {
  final int totalFiles;
  final List<AnalysisIssue> issues;
  final DateTime timestamp;
  
  AnalysisResults({
    required this.totalFiles,
    required this.issues,
    required this.timestamp,
  });
  
  bool get hasErrors => issues.any((i) => i.severity == IssueSeverity.error);
  bool get hasWarnings => issues.any((i) => i.severity == IssueSeverity.warning);
  
  Map<String, dynamic> toJson() {
    return {
      'totalFiles': totalFiles,
      'timestamp': timestamp.toIso8601String(),
      'summary': {
        'total': issues.length,
        'errors': issues.where((i) => i.severity == IssueSeverity.error).length,
        'warnings': issues.where((i) => i.severity == IssueSeverity.warning).length,
        'info': issues.where((i) => i.severity == IssueSeverity.info).length,
      },
      'issues': issues.map((i) => i.toJson()).toList(),
    };
  }
}

class AnalysisIssue {
  final IssueType type;
  final IssueSeverity severity;
  final String file;
  final int line;
  final String message;
  final String suggestion;
  
  AnalysisIssue({
    required this.type,
    required this.severity,
    required this.file,
    required this.line,
    required this.message,
    this.suggestion = '',
  });
  
  Map<String, dynamic> toJson() {
    return {
      'type': type.name,
      'severity': severity.name,
      'file': file,
      'line': line,
      'message': message,
      'suggestion': suggestion,
    };
  }
}

enum IssueType {
  platformCheck,
  navigation,
  dialog,
  icons,
  network,
  validation,
}

enum IssueSeverity {
  error,
  warning,
  info,
}
